import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdProvider extends ChangeNotifier {
  InterstitialAd? _interstitialAd;
  bool _isInterstitialAdReady = false;
  
  // Test Ad Unit IDs (Replace with your actual Ad Unit IDs)
  static const String _interstitialAdUnitId = kDebugMode
      ? 'ca-app-pub-3940256099942544/**********' // Test ID
      : 'YOUR_ACTUAL_INTERSTITIAL_AD_UNIT_ID';
  
  static const String _nativeAdUnitId = kDebugMode
      ? 'ca-app-pub-3940256099942544/**********' // Test ID
      : 'YOUR_ACTUAL_NATIVE_AD_UNIT_ID';

  bool get isInterstitialAdReady => _isInterstitialAdReady;
  String get nativeAdUnitId => _nativeAdUnitId;

  AdProvider() {
    _loadInterstitialAd();
  }

  // Load Interstitial Ad
  void _loadInterstitialAd() {
    InterstitialAd.load(
      adUnitId: _interstitialAdUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          _isInterstitialAdReady = true;
          notifyListeners();
          
          _interstitialAd!.setImmersiveMode(true);
        },
        onAdFailedToLoad: (error) {
          debugPrint('InterstitialAd failed to load: $error');
          _isInterstitialAdReady = false;
          notifyListeners();
        },
      ),
    );
  }

  // Show Interstitial Ad
  Future<void> showInterstitialAd() async {
    if (_isInterstitialAdReady && _interstitialAd != null) {
      _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdShowedFullScreenContent: (ad) {
          debugPrint('Interstitial ad showed full screen content.');
        },
        onAdDismissedFullScreenContent: (ad) {
          debugPrint('Interstitial ad dismissed.');
          ad.dispose();
          _isInterstitialAdReady = false;
          notifyListeners();
          _loadInterstitialAd(); // Load next ad
        },
        onAdFailedToShowFullScreenContent: (ad, error) {
          debugPrint('Interstitial ad failed to show: $error');
          ad.dispose();
          _isInterstitialAdReady = false;
          notifyListeners();
          _loadInterstitialAd(); // Load next ad
        },
      );
      
      await _interstitialAd!.show();
    } else {
      debugPrint('Interstitial ad not ready yet.');
      _loadInterstitialAd(); // Try to load if not ready
    }
  }

  // Create Native Ad
  NativeAd createNativeAd({
    required Function(Ad ad) onAdLoaded,
    required Function(Ad ad, LoadAdError error) onAdFailedToLoad,
  }) {
    return NativeAd(
      adUnitId: _nativeAdUnitId,
      request: const AdRequest(),
      listener: NativeAdListener(
        onAdLoaded: onAdLoaded,
        onAdFailedToLoad: onAdFailedToLoad,
        onAdClicked: (ad) {
          debugPrint('Native ad clicked.');
        },
        onAdImpression: (ad) {
          debugPrint('Native ad impression.');
        },
      ),
      nativeTemplateStyle: NativeTemplateStyle(
        templateType: TemplateType.medium,
        mainBackgroundColor: const Color(0xFFFFFFFF),
        cornerRadius: 12.0,
        callToActionTextStyle: NativeTemplateTextStyle(
          textColor: const Color(0xFFFFFFFF),
          backgroundColor: const Color(0xFF6366F1),
          style: NativeTemplateFontStyle.bold,
          size: 16.0,
        ),
        primaryTextStyle: NativeTemplateTextStyle(
          textColor: const Color(0xFF1E293B),
          style: NativeTemplateFontStyle.bold,
          size: 16.0,
        ),
        secondaryTextStyle: NativeTemplateTextStyle(
          textColor: const Color(0xFF64748B),
          style: NativeTemplateFontStyle.normal,
          size: 14.0,
        ),
        tertiaryTextStyle: NativeTemplateTextStyle(
          textColor: const Color(0xFF94A3B8),
          style: NativeTemplateFontStyle.normal,
          size: 12.0,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _interstitialAd?.dispose();
    super.dispose();
  }
}
