import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter/return_code.dart';
import 'package:android_intent_plus/android_intent.dart';

class VideoService {
  static const MethodChannel _channel = MethodChannel('mediacraft/wallpaper');

  /// Set video as live wallpaper
  static Future<bool> setVideoAsWallpaper({
    required String videoPath,
    required bool setHomeScreen,
    required bool setLockScreen,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      // For Android, we need to convert video to a format suitable for live wallpaper
      final outputPath = await _prepareVideoForWallpaper(
        videoPath,
        onProgress: (progress) => onProgress?.call(0.1 + (progress * 0.7)),
      );

      onProgress?.call(0.8);

      if (outputPath == null) {
        return false;
      }

      // Set as wallpaper using Android Intent
      final success = await _setWallpaperViaIntent(
        outputPath,
        setHomeScreen,
        setLockScreen,
      );

      onProgress?.call(1.0);
      return success;
    } catch (e) {
      debugPrint('Error setting video as wallpaper: $e');
      return false;
    }
  }

  /// Prepare video for wallpaper (optimize format and size)
  static Future<String?> _prepareVideoForWallpaper(
    String inputPath,
    {Function(double)? onProgress}
  ) async {
    try {
      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/wallpaper_video.mp4';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      // FFmpeg command to optimize video for wallpaper
      final command = '-i "$inputPath" '
          '-c:v libx264 '
          '-preset fast '
          '-crf 23 '
          '-c:a aac '
          '-b:a 128k '
          '-movflags +faststart '
          '-vf "scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2" '
          '"$outputPath"';

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        return outputPath;
      } else {
        debugPrint('FFmpeg failed with return code: $returnCode');
        return null;
      }
    } catch (e) {
      debugPrint('Error preparing video for wallpaper: $e');
      return null;
    }
  }

  /// Set wallpaper using Android Intent
  static Future<bool> _setWallpaperViaIntent(
    String videoPath,
    bool setHomeScreen,
    bool setLockScreen,
  ) async {
    try {
      if (Platform.isAndroid) {
        // Use Android Intent to open wallpaper picker
        final intent = AndroidIntent(
          action: 'android.intent.action.SET_WALLPAPER',
          type: 'video/*',
          data: 'file://$videoPath',
        );
        
        await intent.launch();
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error setting wallpaper via intent: $e');
      return false;
    }
  }

  /// Add watermark to video (lossless)
  static Future<String?> addWatermarkToVideo({
    required String inputPath,
    String? watermarkText,
    String? watermarkImagePath,
    required double x,
    required double y,
    double opacity = 1.0,
    Function(double)? onProgress,
  }) async {
    try {
      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/watermarked_video.mp4';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      String filterComplex;
      
      if (watermarkText != null && watermarkText.isNotEmpty) {
        // Text watermark
        filterComplex = 'drawtext=text=\'$watermarkText\':'
            'fontcolor=white:'
            'fontsize=24:'
            'x=$x:'
            'y=$y:'
            'alpha=$opacity';
      } else if (watermarkImagePath != null) {
        // Image watermark
        filterComplex = '[0:v][1:v] overlay=$x:$y:alpha=$opacity [v]';
      } else {
        return null;
      }

      String command;
      if (watermarkImagePath != null) {
        command = '-i "$inputPath" -i "$watermarkImagePath" '
            '-filter_complex "$filterComplex" '
            '-map "[v]" -map 0:a '
            '-c:v libx264 -preset ultrafast -crf 0 '
            '-c:a copy '
            '"$outputPath"';
      } else {
        command = '-i "$inputPath" '
            '-vf "$filterComplex" '
            '-c:v libx264 -preset ultrafast -crf 0 '
            '-c:a copy '
            '"$outputPath"';
      }

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      if (ReturnCode.isSuccess(returnCode)) {
        return outputPath;
      } else {
        debugPrint('FFmpeg watermark failed with return code: $returnCode');
        return null;
      }
    } catch (e) {
      debugPrint('Error adding watermark to video: $e');
      return null;
    }
  }

  /// Get video information
  static Future<Map<String, dynamic>?> getVideoInfo(String videoPath) async {
    try {
      final command = '-i "$videoPath" -hide_banner';
      final session = await FFmpegKit.execute(command);
      final output = await session.getOutput();
      
      // Parse video information from FFmpeg output
      // This is a simplified version - you might want to use ffprobe for more detailed info
      return {
        'path': videoPath,
        'duration': 0, // Parse from output
        'width': 0,    // Parse from output
        'height': 0,   // Parse from output
        'fps': 0,      // Parse from output
      };
    } catch (e) {
      debugPrint('Error getting video info: $e');
      return null;
    }
  }
}
