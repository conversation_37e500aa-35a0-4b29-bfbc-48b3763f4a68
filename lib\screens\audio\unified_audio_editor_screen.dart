import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:audioplayers/audioplayers.dart' as ap;
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:share_plus/share_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';

import '../../core/app_theme.dart';
import '../../providers/app_state_provider.dart';
import '../../providers/ad_provider.dart';
import '../../services/audio_service.dart';

import '../../widgets/native_ad_widget.dart';

class UnifiedAudioEditorScreen extends StatefulWidget {
  const UnifiedAudioEditorScreen({super.key});

  @override
  State<UnifiedAudioEditorScreen> createState() =>
      _UnifiedAudioEditorScreenState();
}

class _UnifiedAudioEditorScreenState extends State<UnifiedAudioEditorScreen>
    with TickerProviderStateMixin {
  String? _selectedAudioPath;
  ap.AudioPlayer? _audioPlayer;
  PlayerController? _waveformController;

  Duration _totalDuration = Duration.zero;
  Duration _currentPosition = Duration.zero;
  Duration _startTime = Duration.zero;
  Duration _endTime = Duration.zero;

  bool _isLoading = false;
  bool _isPlaying = false;
  bool _isProcessing = false;

  // Audio Effects
  double _reverbRoomSize = 0.5;
  double _reverbDamping = 0.5;
  double _reverbWetLevel = 0.3;
  double _playbackSpeed = 1.0;
  double _volume = 1.0;
  double _pitch = 1.0;

  // Waveform handles
  double _startHandlePosition = 0.0;
  double _endHandlePosition = 1.0;
  double _playheadPosition = 0.0;

  late AnimationController _playheadController;
  late Animation<double> _playheadAnimation;

  @override
  void initState() {
    super.initState();
    _audioPlayer = ap.AudioPlayer();
    _waveformController = PlayerController();

    _playheadController = AnimationController(
      duration: Duration.zero,
      vsync: this,
    );

    _playheadAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_playheadController);

    _audioPlayer!.onDurationChanged.listen((duration) {
      setState(() {
        _totalDuration = duration;
        _endTime = duration;
      });
    });

    _audioPlayer!.onPositionChanged.listen((position) {
      setState(() {
        _currentPosition = position;
        if (_totalDuration.inMilliseconds > 0) {
          _playheadPosition =
              position.inMilliseconds / _totalDuration.inMilliseconds;
        }
      });
    });

    _audioPlayer!.onPlayerStateChanged.listen((state) {
      setState(() {
        _isPlaying = state == ap.PlayerState.playing;
      });
    });
  }

  @override
  void dispose() {
    _audioPlayer?.dispose();
    _waveformController?.dispose();
    _playheadController.dispose();
    super.dispose();
  }

  Future<void> _pickAudioFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.path != null) {
          setState(() {
            _selectedAudioPath = file.path;
            _isLoading = true;
          });
          await _loadAudio();
        }
      }
    } catch (e) {
      _showErrorSnackBar('Error picking audio file: $e');
    }
  }

  Future<void> _loadAudio() async {
    if (_selectedAudioPath == null) return;

    try {
      await _audioPlayer!.setSourceDeviceFile(_selectedAudioPath!);
      await _waveformController!.preparePlayer(
        path: _selectedAudioPath!,
        shouldExtractWaveform: true,
      );

      setState(() {
        _isLoading = false;
        _startHandlePosition = 0.0;
        _endHandlePosition = 1.0;
        _playheadPosition = 0.0;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Error loading audio: $e');
    }
  }

  Future<void> _playPause() async {
    if (_audioPlayer == null) return;

    try {
      if (_isPlaying) {
        await _audioPlayer!.pause();
      } else {
        // Start playing from the start handle position
        final startPosition = Duration(
          milliseconds:
              (_startHandlePosition * _totalDuration.inMilliseconds).round(),
        );
        await _audioPlayer!.seek(startPosition);
        await _audioPlayer!.resume();
      }
    } catch (e) {
      _showErrorSnackBar('Error playing audio: $e');
    }
  }

  Future<void> _seekToPosition(double position) async {
    if (_audioPlayer == null || _totalDuration == Duration.zero) return;

    final seekPosition = Duration(
      milliseconds: (position * _totalDuration.inMilliseconds).round(),
    );

    await _audioPlayer!.seek(seekPosition);
  }

  void _updateStartHandle(double position) {
    setState(() {
      _startHandlePosition = position.clamp(0.0, _endHandlePosition - 0.01);
      _startTime = Duration(
        milliseconds:
            (_startHandlePosition * _totalDuration.inMilliseconds).round(),
      );
    });
  }

  void _updateEndHandle(double position) {
    setState(() {
      _endHandlePosition = position.clamp(_startHandlePosition + 0.01, 1.0);
      _endTime = Duration(
        milliseconds:
            (_endHandlePosition * _totalDuration.inMilliseconds).round(),
      );
    });
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Audio Editor'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // File Selection Card
                _buildFileSelectionCard(),

                const SizedBox(height: 16),

                // Waveform and Controls
                if (_selectedAudioPath != null) ...[
                  _buildWaveformCard(),
                  const SizedBox(height: 16),
                  _buildAudioControlsCard(),
                  const SizedBox(height: 16),
                  _buildEffectsCard(),
                  const SizedBox(height: 16),
                  _buildActionButtonsCard(),
                ],

                const SizedBox(height: 16),

                // Native Ad
                Consumer<AdProvider>(
                  builder: (context, adProvider, child) {
                    return const NativeAdWidget();
                  },
                ),
              ],
            ),
          ),

          // Loading Overlay
          if (_isLoading || _isProcessing)
            Container(
              color: Colors.black54,
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFileSelectionCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.audiotrack,
              size: 48,
              color: AppTheme.primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              _selectedAudioPath == null
                  ? 'Select Audio File'
                  : 'Audio File Selected',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (_selectedAudioPath != null) ...[
              const SizedBox(height: 8),
              Text(
                _selectedAudioPath!.split('/').last,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _pickAudioFile,
              icon: const Icon(Icons.folder_open),
              label: Text(
                  _selectedAudioPath == null ? 'Choose File' : 'Change File'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWaveformCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Waveform Editor',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Time indicators
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Start: ${_formatDuration(_startTime)}',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Current: ${_formatDuration(_currentPosition)}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  'End: ${_formatDuration(_endTime)}',
                  style: TextStyle(
                    color: AppTheme.accentColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Waveform with handles
            Container(
              height: 120,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Stack(
                children: [
                  // Waveform
                  Positioned.fill(
                    child: AudioFileWaveforms(
                      size: const Size(double.infinity, 120),
                      playerController: _waveformController!,
                      waveformType: WaveformType.long,
                      playerWaveStyle: PlayerWaveStyle(
                        fixedWaveColor: Colors.grey.shade400,
                        liveWaveColor: AppTheme.primaryColor,
                        spacing: 6,
                        showSeekLine: false,
                      ),
                    ),
                  ),

                  // Selection overlay
                  Positioned.fill(
                    child: CustomPaint(
                      painter: WaveformOverlayPainter(
                        startPosition: _startHandlePosition,
                        endPosition: _endHandlePosition,
                        playheadPosition: _playheadPosition,
                      ),
                    ),
                  ),

                  // Start handle
                  Positioned(
                    left: _startHandlePosition *
                            (MediaQuery.of(context).size.width - 64) -
                        12,
                    top: 0,
                    bottom: 0,
                    child: GestureDetector(
                      onPanUpdate: (details) {
                        final RenderBox box =
                            context.findRenderObject() as RenderBox;
                        final localPosition =
                            box.globalToLocal(details.globalPosition);
                        final newPosition = localPosition.dx /
                            (MediaQuery.of(context).size.width - 64);
                        _updateStartHandle(newPosition);
                      },
                      child: Container(
                        width: 24,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.drag_handle,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ),

                  // End handle
                  Positioned(
                    left: _endHandlePosition *
                            (MediaQuery.of(context).size.width - 64) -
                        12,
                    top: 0,
                    bottom: 0,
                    child: GestureDetector(
                      onPanUpdate: (details) {
                        final RenderBox box =
                            context.findRenderObject() as RenderBox;
                        final localPosition =
                            box.globalToLocal(details.globalPosition);
                        final newPosition = localPosition.dx /
                            (MediaQuery.of(context).size.width - 64);
                        _updateEndHandle(newPosition);
                      },
                      child: Container(
                        width: 24,
                        decoration: BoxDecoration(
                          color: AppTheme.accentColor,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.drag_handle,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Selection info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Selection: ${_formatDuration(_endTime - _startTime)}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    'Total: ${_formatDuration(_totalDuration)}',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAudioControlsCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Text(
              'Audio Controls',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Play/Pause and Seek Controls
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () async {
                    await _seekToPosition(_startHandlePosition);
                  },
                  icon: const Icon(Icons.skip_previous),
                  iconSize: 32,
                ),
                IconButton(
                  onPressed: _playPause,
                  icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
                  iconSize: 48,
                  style: IconButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
                IconButton(
                  onPressed: () async {
                    await _seekToPosition(_endHandlePosition);
                  },
                  icon: const Icon(Icons.skip_next),
                  iconSize: 32,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Volume Control
            Row(
              children: [
                const Icon(Icons.volume_down),
                Expanded(
                  child: Slider(
                    value: _volume,
                    min: 0.0,
                    max: 1.0,
                    divisions: 20,
                    label: '${(_volume * 100).round()}%',
                    onChanged: (value) {
                      setState(() {
                        _volume = value;
                      });
                      _audioPlayer?.setVolume(value);
                    },
                  ),
                ),
                const Icon(Icons.volume_up),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEffectsCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Audio Effects',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Speed Control
            _buildEffectSlider(
              'Playback Speed',
              _playbackSpeed,
              0.5,
              2.0,
              '${_playbackSpeed.toStringAsFixed(1)}x',
              (value) {
                setState(() {
                  _playbackSpeed = value;
                });
                _audioPlayer?.setPlaybackRate(value);
              },
            ),

            const SizedBox(height: 16),

            // Reverb Controls
            const Text(
              'Reverb Settings',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),

            _buildEffectSlider(
              'Room Size',
              _reverbRoomSize,
              0.0,
              1.0,
              '${(_reverbRoomSize * 100).round()}%',
              (value) {
                setState(() {
                  _reverbRoomSize = value;
                });
              },
            ),

            _buildEffectSlider(
              'Damping',
              _reverbDamping,
              0.0,
              1.0,
              '${(_reverbDamping * 100).round()}%',
              (value) {
                setState(() {
                  _reverbDamping = value;
                });
              },
            ),

            _buildEffectSlider(
              'Wet Level',
              _reverbWetLevel,
              0.0,
              1.0,
              '${(_reverbWetLevel * 100).round()}%',
              (value) {
                setState(() {
                  _reverbWetLevel = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEffectSlider(
    String label,
    double value,
    double min,
    double max,
    String displayValue,
    ValueChanged<double> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(label),
            Text(
              displayValue,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: 20,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildActionButtonsCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const Text(
              'Actions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Action Buttons Grid
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              childAspectRatio: 2.5,
              children: [
                _buildActionButton(
                  'Cut Audio',
                  Icons.content_cut,
                  AppTheme.primaryColor,
                  _cutAudio,
                ),
                _buildActionButton(
                  'Apply Effects',
                  Icons.auto_fix_high,
                  AppTheme.accentColor,
                  _applyEffects,
                ),
                _buildActionButton(
                  'Save to Device',
                  Icons.save,
                  Colors.green,
                  _saveToDevice,
                ),
                _buildActionButton(
                  'Set as Ringtone',
                  Icons.ring_volume,
                  Colors.orange,
                  _setAsRingtone,
                ),
                _buildActionButton(
                  'Share',
                  Icons.share,
                  Colors.blue,
                  _shareAudio,
                ),
                _buildActionButton(
                  'Preview',
                  Icons.preview,
                  Colors.purple,
                  _previewSelection,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18),
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  // Action Methods
  Future<void> _cutAudio() async {
    if (_selectedAudioPath == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final result = await AudioService.cutAudio(
        inputPath: _selectedAudioPath!,
        startTime: _startTime,
        endTime: _endTime,
        onProgress: (progress) {
          // Update progress if needed
        },
      );

      if (result != null) {
        _showSuccessSnackBar('Audio cut successfully!');
        setState(() {
          _selectedAudioPath = result;
        });
        await _loadAudio();
      } else {
        _showErrorSnackBar('Failed to cut audio');
      }
    } catch (e) {
      _showErrorSnackBar('Error cutting audio: $e');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _applyEffects() async {
    if (_selectedAudioPath == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      String? result = _selectedAudioPath;

      // Apply reverb effect
      if (_reverbWetLevel > 0.1) {
        result = await AudioService.applyReverbEffect(
          inputPath: result!,
          roomSize: _reverbRoomSize,
          damping: _reverbDamping,
          wetLevel: _reverbWetLevel,
          onProgress: (progress) {
            // Update progress if needed
          },
        );
      }

      // Apply speed effect
      if (_playbackSpeed != 1.0) {
        result = await AudioService.applySlowMotionEffect(
          inputPath: result!,
          speed: _playbackSpeed,
          onProgress: (progress) {
            // Update progress if needed
          },
        );
      }

      if (result != null) {
        _showSuccessSnackBar('Effects applied successfully!');
        setState(() {
          _selectedAudioPath = result;
        });
        await _loadAudio();
      } else {
        _showErrorSnackBar('Failed to apply effects');
      }
    } catch (e) {
      _showErrorSnackBar('Error applying effects: $e');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _saveToDevice() async {
    if (_selectedAudioPath == null) return;

    try {
      // Request storage permission
      final permission = await Permission.storage.request();
      if (!permission.isGranted) {
        _showErrorSnackBar('Storage permission required');
        return;
      }

      // Copy file to Downloads directory
      final file = File(_selectedAudioPath!);
      final fileName =
          'edited_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';
      final downloadsDir = Directory('/storage/emulated/0/Download');
      final newPath = '${downloadsDir.path}/$fileName';

      await file.copy(newPath);
      _showSuccessSnackBar('Audio saved to Downloads folder');
    } catch (e) {
      _showErrorSnackBar('Error saving audio: $e');
    }
  }

  Future<void> _setAsRingtone() async {
    if (_selectedAudioPath == null) return;

    try {
      final success = await AudioService.setAsRingtone(_selectedAudioPath!);
      if (success) {
        _showSuccessSnackBar('Ringtone set successfully!');
      } else {
        _showErrorSnackBar('Failed to set ringtone');
      }
    } catch (e) {
      _showErrorSnackBar('Error setting ringtone: $e');
    }
  }

  Future<void> _shareAudio() async {
    if (_selectedAudioPath == null) return;

    try {
      await Share.shareXFiles(
        [XFile(_selectedAudioPath!)],
        text: 'Check out this edited audio!',
      );
    } catch (e) {
      _showErrorSnackBar('Error sharing audio: $e');
    }
  }

  Future<void> _previewSelection() async {
    if (_audioPlayer == null) return;

    try {
      // Stop current playback
      await _audioPlayer!.stop();

      // Seek to start position
      await _audioPlayer!.seek(_startTime);

      // Play for the selected duration
      await _audioPlayer!.resume();

      // Stop at end position
      Future.delayed(_endTime - _startTime, () async {
        await _audioPlayer!.pause();
      });

      _showSuccessSnackBar('Playing selection...');
    } catch (e) {
      _showErrorSnackBar('Error previewing selection: $e');
    }
  }
}

// Custom painter for waveform overlay
class WaveformOverlayPainter extends CustomPainter {
  final double startPosition;
  final double endPosition;
  final double playheadPosition;

  WaveformOverlayPainter({
    required this.startPosition,
    required this.endPosition,
    required this.playheadPosition,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    // Draw selection area
    paint.color = AppTheme.primaryColor.withOpacity(0.3);
    final selectionRect = Rect.fromLTRB(
      startPosition * size.width,
      0,
      endPosition * size.width,
      size.height,
    );
    canvas.drawRect(selectionRect, paint);

    // Draw selection borders
    paint.color = AppTheme.primaryColor;
    paint.strokeWidth = 2;

    // Start line
    canvas.drawLine(
      Offset(startPosition * size.width, 0),
      Offset(startPosition * size.width, size.height),
      paint,
    );

    // End line
    canvas.drawLine(
      Offset(endPosition * size.width, 0),
      Offset(endPosition * size.width, size.height),
      paint,
    );

    // Draw playhead
    paint.color = Colors.red;
    paint.strokeWidth = 3;
    canvas.drawLine(
      Offset(playheadPosition * size.width, 0),
      Offset(playheadPosition * size.width, size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
