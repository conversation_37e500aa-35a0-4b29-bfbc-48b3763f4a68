import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter/return_code.dart';

class AudioService {
  static const MethodChannel _channel = MethodChannel('mediacraft/audio');

  /// Cut audio file
  static Future<String?> cutAudio({
    required String inputPath,
    required Duration startTime,
    required Duration endTime,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/cut_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.3);

      // FFmpeg command to cut audio
      final startSeconds = startTime.inSeconds + (startTime.inMilliseconds % 1000) / 1000.0;
      final duration = (endTime - startTime).inSeconds + ((endTime - startTime).inMilliseconds % 1000) / 1000.0;
      
      final command = '-i "$inputPath" '
          '-ss $startSeconds '
          '-t $duration '
          '-c copy '
          '"$outputPath"';

      onProgress?.call(0.5);

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      onProgress?.call(0.9);

      if (ReturnCode.isSuccess(returnCode)) {
        onProgress?.call(1.0);
        return outputPath;
      } else {
        debugPrint('FFmpeg cut audio failed with return code: $returnCode');
        return null;
      }
    } catch (e) {
      debugPrint('Error cutting audio: $e');
      return null;
    }
  }

  /// Apply reverb effect to audio
  static Future<String?> applyReverbEffect({
    required String inputPath,
    double roomSize = 0.5,
    double damping = 0.5,
    double wetLevel = 0.3,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/reverb_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.3);

      // FFmpeg command to apply reverb effect
      final command = '-i "$inputPath" '
          '-af "aecho=0.8:0.9:1000:0.3" '
          '-c:a libmp3lame '
          '-b:a 192k '
          '"$outputPath"';

      onProgress?.call(0.5);

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      onProgress?.call(0.9);

      if (ReturnCode.isSuccess(returnCode)) {
        onProgress?.call(1.0);
        return outputPath;
      } else {
        debugPrint('FFmpeg reverb effect failed with return code: $returnCode');
        return null;
      }
    } catch (e) {
      debugPrint('Error applying reverb effect: $e');
      return null;
    }
  }

  /// Apply slow motion effect to audio
  static Future<String?> applySlowMotionEffect({
    required String inputPath,
    double speed = 0.5, // 0.5 = half speed, 2.0 = double speed
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/slow_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.3);

      // FFmpeg command to change audio speed
      final command = '-i "$inputPath" '
          '-filter:a "atempo=$speed" '
          '-c:a libmp3lame '
          '-b:a 192k '
          '"$outputPath"';

      onProgress?.call(0.5);

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      onProgress?.call(0.9);

      if (ReturnCode.isSuccess(returnCode)) {
        onProgress?.call(1.0);
        return outputPath;
      } else {
        debugPrint('FFmpeg slow motion effect failed with return code: $returnCode');
        return null;
      }
    } catch (e) {
      debugPrint('Error applying slow motion effect: $e');
      return null;
    }
  }

  /// Reverse audio
  static Future<String?> reverseAudio({
    required String inputPath,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/reverse_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.3);

      // FFmpeg command to reverse audio
      final command = '-i "$inputPath" '
          '-af "areverse" '
          '-c:a libmp3lame '
          '-b:a 192k '
          '"$outputPath"';

      onProgress?.call(0.5);

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      onProgress?.call(0.9);

      if (ReturnCode.isSuccess(returnCode)) {
        onProgress?.call(1.0);
        return outputPath;
      } else {
        debugPrint('FFmpeg reverse audio failed with return code: $returnCode');
        return null;
      }
    } catch (e) {
      debugPrint('Error reversing audio: $e');
      return null;
    }
  }

  /// Set audio as ringtone (Android)
  static Future<bool> setAsRingtone(String audioPath) async {
    try {
      if (Platform.isAndroid) {
        final result = await _channel.invokeMethod('setRingtone', {
          'audioPath': audioPath,
        });
        return result == true;
      }
      return false;
    } catch (e) {
      debugPrint('Error setting ringtone: $e');
      return false;
    }
  }

  /// Get audio information
  static Future<Map<String, dynamic>?> getAudioInfo(String audioPath) async {
    try {
      final command = '-i "$audioPath" -hide_banner';
      final session = await FFmpegKit.execute(command);
      final output = await session.getOutput();
      
      // Parse audio information from FFmpeg output
      // This is a simplified version - you might want to use ffprobe for more detailed info
      return {
        'path': audioPath,
        'duration': 0, // Parse from output
        'bitrate': 0,  // Parse from output
        'sampleRate': 0, // Parse from output
        'channels': 0,   // Parse from output
      };
    } catch (e) {
      debugPrint('Error getting audio info: $e');
      return null;
    }
  }

  /// Convert audio format
  static Future<String?> convertAudioFormat({
    required String inputPath,
    required String outputFormat, // mp3, wav, aac, etc.
    int bitrate = 192,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/converted_audio_${DateTime.now().millisecondsSinceEpoch}.$outputFormat';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.3);

      // FFmpeg command to convert audio format
      String codec;
      switch (outputFormat.toLowerCase()) {
        case 'mp3':
          codec = 'libmp3lame';
          break;
        case 'aac':
          codec = 'aac';
          break;
        case 'wav':
          codec = 'pcm_s16le';
          break;
        default:
          codec = 'libmp3lame';
      }

      final command = '-i "$inputPath" '
          '-c:a $codec '
          '-b:a ${bitrate}k '
          '"$outputPath"';

      onProgress?.call(0.5);

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      onProgress?.call(0.9);

      if (ReturnCode.isSuccess(returnCode)) {
        onProgress?.call(1.0);
        return outputPath;
      } else {
        debugPrint('FFmpeg audio conversion failed with return code: $returnCode');
        return null;
      }
    } catch (e) {
      debugPrint('Error converting audio format: $e');
      return null;
    }
  }
}
