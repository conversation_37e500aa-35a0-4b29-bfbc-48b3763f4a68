import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
// import 'package:ffmpeg_kit_flutter/return_code.dart';

class AudioService {
  static const MethodChannel _channel = MethodChannel('mediacraft/audio');

  /// Cut audio file (simplified version without FFmpeg)
  static Future<String?> cutAudio({
    required String inputPath,
    required Duration startTime,
    required Duration endTime,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/cut_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.5);

      // FFmpeg command to cut audio with precise timing
      final startSeconds =
          startTime.inSeconds + (startTime.inMilliseconds % 1000) / 1000.0;
      final duration = (endTime - startTime).inSeconds +
          ((endTime - startTime).inMilliseconds % 1000) / 1000.0;

      final command = '-i "$inputPath" '
          '-ss $startSeconds '
          '-t $duration '
          '-c copy '
          '-avoid_negative_ts make_zero '
          '"$outputPath"';

      onProgress?.call(0.5);

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      onProgress?.call(0.9);

      if (ReturnCode.isSuccess(returnCode)) {
        onProgress?.call(1.0);
        return outputPath;
      } else {
        final logs = await session.getLogs();
        debugPrint('FFmpeg cut audio failed with return code: $returnCode');
        for (final log in logs) {
          debugPrint('FFmpeg log: ${await log.getMessage()}');
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error cutting audio: $e');
      return null;
    }
  }

  /// Apply reverb effect (simplified version)
  static Future<String?> applyReverbEffect({
    required String inputPath,
    double roomSize = 0.5,
    double damping = 0.5,
    double wetLevel = 0.3,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/reverb_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.5);

      // FFmpeg command to apply reverb effect using aecho filter
      final delayMs = (roomSize * 1000).round();
      final decayFactor = 1.0 - damping;

      final command = '-i "$inputPath" '
          '-af "aecho=0.8:0.9:$delayMs:$wetLevel,aecho=0.4:0.5:${(delayMs * 1.5).round()}:${wetLevel * 0.6}" '
          '-c:a aac '
          '-b:a 192k '
          '"$outputPath"';

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      onProgress?.call(0.9);

      if (ReturnCode.isSuccess(returnCode)) {
        onProgress?.call(1.0);
        return outputPath;
      } else {
        final logs = await session.getLogs();
        debugPrint('FFmpeg reverb failed with return code: $returnCode');
        for (final log in logs) {
          debugPrint('FFmpeg log: ${await log.getMessage()}');
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error applying reverb effect: $e');
      return null;
    }
  }

  /// Apply slow motion effect (simplified version)
  static Future<String?> applySlowMotionEffect({
    required String inputPath,
    double speed = 0.5,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/slow_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.5);

      // FFmpeg command to apply speed effect using atempo filter
      // atempo filter accepts values between 0.5 and 100.0
      final clampedSpeed = speed.clamp(0.5, 2.0);

      final command = '-i "$inputPath" '
          '-filter:a "atempo=$clampedSpeed" '
          '-c:a aac '
          '-b:a 192k '
          '"$outputPath"';

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      onProgress?.call(0.9);

      if (ReturnCode.isSuccess(returnCode)) {
        onProgress?.call(1.0);
        return outputPath;
      } else {
        final logs = await session.getLogs();
        debugPrint('FFmpeg speed effect failed with return code: $returnCode');
        for (final log in logs) {
          debugPrint('FFmpeg log: ${await log.getMessage()}');
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error applying slow motion effect: $e');
      return null;
    }
  }

  /// Reverse audio (simplified version)
  static Future<String?> reverseAudio({
    required String inputPath,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/reverse_audio_${DateTime.now().millisecondsSinceEpoch}.mp3';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.5);

      // For now, just copy the original file
      // In a production app, you would use FFmpeg or similar for audio reversal
      final inputFile = File(inputPath);
      await inputFile.copy(outputPath);

      onProgress?.call(1.0);
      return outputPath;
    } catch (e) {
      debugPrint('Error reversing audio: $e');
      return null;
    }
  }

  /// Set audio as ringtone (Android)
  static Future<bool> setAsRingtone(String audioPath) async {
    try {
      if (Platform.isAndroid) {
        final result = await _channel.invokeMethod('setRingtone', {
          'audioPath': audioPath,
        });
        return result == true;
      }
      return false;
    } catch (e) {
      debugPrint('Error setting ringtone: $e');
      return false;
    }
  }

  /// Get audio information (simplified version)
  static Future<Map<String, dynamic>?> getAudioInfo(String audioPath) async {
    try {
      // Return basic file information
      final file = File(audioPath);
      final fileSize = await file.length();

      return {
        'path': audioPath,
        'duration': 0, // Would need audio analysis library
        'bitrate': 0, // Would need audio analysis library
        'sampleRate': 0, // Would need audio analysis library
        'channels': 0, // Would need audio analysis library
        'fileSize': fileSize,
      };
    } catch (e) {
      debugPrint('Error getting audio info: $e');
      return null;
    }
  }

  /// Convert audio format (simplified version)
  static Future<String?> convertAudioFormat({
    required String inputPath,
    required String outputFormat,
    int bitrate = 192,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/converted_audio_${DateTime.now().millisecondsSinceEpoch}.$outputFormat';

      // Delete existing file if it exists
      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      onProgress?.call(0.5);

      // FFmpeg command to convert audio format
      String codec;
      switch (outputFormat.toLowerCase()) {
        case 'mp3':
          codec = 'libmp3lame';
          break;
        case 'aac':
          codec = 'aac';
          break;
        case 'wav':
          codec = 'pcm_s16le';
          break;
        case 'ogg':
          codec = 'libvorbis';
          break;
        default:
          codec = 'aac';
      }

      final command = '-i "$inputPath" '
          '-c:a $codec '
          '-b:a ${bitrate}k '
          '"$outputPath"';

      final session = await FFmpegKit.execute(command);
      final returnCode = await session.getReturnCode();

      onProgress?.call(0.9);

      if (ReturnCode.isSuccess(returnCode)) {
        onProgress?.call(1.0);
        return outputPath;
      } else {
        final logs = await session.getLogs();
        debugPrint(
            'FFmpeg format conversion failed with return code: $returnCode');
        for (final log in logs) {
          debugPrint('FFmpeg log: ${await log.getMessage()}');
        }
        return null;
      }
    } catch (e) {
      debugPrint('Error converting audio format: $e');
      return null;
    }
  }
}
