package com.example.mediacraft

import android.content.ContentValues
import android.content.Intent
import android.media.RingtoneManager
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.provider.Settings
import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.File

class MainActivity: FlutterActivity() {
    private val AUDIO_CHANNEL = "mediacraft/audio"
    private val WALLPAPER_CHANNEL = "mediacraft/wallpaper"

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        // Audio channel for ringtone functionality
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, AUDIO_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "setRingtone" -> {
                    val audioPath = call.argument<String>("audioPath")
                    if (audioPath != null) {
                        setRingtone(audioPath, result)
                    } else {
                        result.error("INVALID_ARGUMENT", "Audio path is required", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
        
        // Wallpaper channel for wallpaper functionality
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, WALLPAPER_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "setWallpaper" -> {
                    val videoPath = call.argument<String>("videoPath")
                    val setHomeScreen = call.argument<Boolean>("setHomeScreen") ?: true
                    val setLockScreen = call.argument<Boolean>("setLockScreen") ?: true
                    
                    if (videoPath != null) {
                        setWallpaper(videoPath, setHomeScreen, setLockScreen, result)
                    } else {
                        result.error("INVALID_ARGUMENT", "Video path is required", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun setRingtone(audioPath: String, result: MethodChannel.Result) {
        try {
            val audioFile = File(audioPath)
            if (!audioFile.exists()) {
                result.error("FILE_NOT_FOUND", "Audio file not found", null)
                return
            }

            // Check if we have permission to write settings
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (!Settings.System.canWrite(this)) {
                    // Request permission to write settings
                    val intent = Intent(Settings.ACTION_MANAGE_WRITE_SETTINGS)
                    intent.data = Uri.parse("package:$packageName")
                    startActivity(intent)
                    result.error("PERMISSION_DENIED", "Permission to write settings is required", null)
                    return
                }
            }

            // Copy file to ringtones directory
            val values = ContentValues().apply {
                put(MediaStore.MediaColumns.DATA, audioPath)
                put(MediaStore.MediaColumns.TITLE, audioFile.nameWithoutExtension)
                put(MediaStore.MediaColumns.MIME_TYPE, "audio/*")
                put(MediaStore.Audio.Media.IS_RINGTONE, true)
                put(MediaStore.Audio.Media.IS_NOTIFICATION, false)
                put(MediaStore.Audio.Media.IS_ALARM, false)
                put(MediaStore.Audio.Media.IS_MUSIC, false)
            }

            val uri = contentResolver.insert(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, values)
            
            if (uri != null) {
                // Set as default ringtone
                RingtoneManager.setActualDefaultRingtoneUri(
                    this,
                    RingtoneManager.TYPE_RINGTONE,
                    uri
                )
                result.success(true)
            } else {
                result.error("FAILED", "Failed to set ringtone", null)
            }
        } catch (e: Exception) {
            result.error("ERROR", "Error setting ringtone: ${e.message}", null)
        }
    }

    private fun setWallpaper(videoPath: String, setHomeScreen: Boolean, setLockScreen: Boolean, result: MethodChannel.Result) {
        try {
            val videoFile = File(videoPath)
            if (!videoFile.exists()) {
                result.error("FILE_NOT_FOUND", "Video file not found", null)
                return
            }

            // For live wallpapers, we need to use an intent to open wallpaper picker
            val intent = Intent(Intent.ACTION_SET_WALLPAPER)
            intent.setDataAndType(Uri.fromFile(videoFile), "video/*")
            intent.putExtra("mimeType", "video/*")
            
            if (intent.resolveActivity(packageManager) != null) {
                startActivity(Intent.createChooser(intent, "Set Live Wallpaper"))
                result.success(true)
            } else {
                // Fallback: try to open with specific live wallpaper apps
                val fallbackIntent = Intent(Intent.ACTION_VIEW)
                fallbackIntent.setDataAndType(Uri.fromFile(videoFile), "video/*")
                fallbackIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                
                if (fallbackIntent.resolveActivity(packageManager) != null) {
                    startActivity(fallbackIntent)
                    result.success(true)
                } else {
                    result.error("NO_APP", "No app found to set live wallpaper", null)
                }
            }
        } catch (e: Exception) {
            result.error("ERROR", "Error setting wallpaper: ${e.message}", null)
        }
    }
}
