import 'package:flutter/material.dart';
import '../screens/home/<USER>';
import '../screens/video/video_to_wallpaper_screen.dart';
import '../screens/video/video_watermark_screen.dart';
import '../screens/audio/mp3_cutter_screen.dart';
import '../screens/audio/audio_editor_screen.dart';
import '../screens/image/image_compressor_screen.dart';
import '../screens/image/image_converter_screen.dart';
import '../screens/pdf/pdf_converter_screen.dart';
import '../screens/pdf/pdf_compressor_screen.dart';

class AppRouter {
  static const String home = '/';
  static const String videoToWallpaper = '/video-to-wallpaper';
  static const String videoWatermark = '/video-watermark';
  static const String mp3Cutter = '/mp3-cutter';
  static const String audioEditor = '/audio-editor';
  static const String imageCompressor = '/image-compressor';
  static const String imageConverter = '/image-converter';
  static const String pdfConverter = '/pdf-converter';
  static const String pdfCompressor = '/pdf-compressor';

  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case home:
        return _createRoute(const HomeScreen());
      
      case videoToWallpaper:
        return _createRoute(const VideoToWallpaperScreen());
      
      case videoWatermark:
        return _createRoute(const VideoWatermarkScreen());
      
      case mp3Cutter:
        return _createRoute(const Mp3CutterScreen());
      
      case audioEditor:
        return _createRoute(const AudioEditorScreen());
      
      case imageCompressor:
        return _createRoute(const ImageCompressorScreen());
      
      case imageConverter:
        return _createRoute(const ImageConverterScreen());
      
      case pdfConverter:
        return _createRoute(const PdfConverterScreen());
      
      case pdfCompressor:
        return _createRoute(const PdfCompressorScreen());
      
      default:
        return _createRoute(
          Scaffold(
            body: Center(
              child: Text('No route defined for ${settings.name}'),
            ),
          ),
        );
    }
  }

  static PageRouteBuilder _createRoute(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }
}
