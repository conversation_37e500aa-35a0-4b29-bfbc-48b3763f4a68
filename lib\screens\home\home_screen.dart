import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import '../../core/app_theme.dart';
import '../../models/tool_model.dart';
import '../../providers/app_state_provider.dart';
import '../../widgets/tool_card.dart';
import '../../widgets/native_ad_widget.dart';
import '../../widgets/app_header.dart';
import '../../widgets/category_filter.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  ToolCategory? _selectedCategory;
  List<ToolModel> _filteredTools = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    _filteredTools = ToolModel.getAllTools();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _filterTools(ToolCategory? category) {
    setState(() {
      _selectedCategory = category;
      if (category == null) {
        _filteredTools = ToolModel.getAllTools();
      } else {
        _filteredTools = ToolModel.getToolsByCategory(category);
      }
    });
  }

  List<Widget> _buildGridItems() {
    final List<Widget> items = [];
    
    for (int i = 0; i < _filteredTools.length; i++) {
      // Add tool card
      items.add(
        AnimatedBuilder(
          animation: _fadeAnimation,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 50 * (1 - _fadeAnimation.value)),
              child: Opacity(
                opacity: _fadeAnimation.value,
                child: ToolCard(
                  tool: _filteredTools[i],
                  delay: Duration(milliseconds: i * 100),
                ),
              ),
            );
          },
        ),
      );
      
      // Add native ad every 3 tools
      if ((i + 1) % 3 == 0 && i < _filteredTools.length - 1) {
        items.add(
          AnimatedBuilder(
            animation: _fadeAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, 50 * (1 - _fadeAnimation.value)),
                child: Opacity(
                  opacity: _fadeAnimation.value,
                  child: const NativeAdWidget(),
                ),
              );
            },
          ),
        );
      }
    }
    
    return items;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // App Header
            const AppHeader(),
            
            // Category Filter
            CategoryFilter(
              selectedCategory: _selectedCategory,
              onCategorySelected: _filterTools,
            ),
            
            // Tools Grid
            Expanded(
              child: Consumer<AppStateProvider>(
                builder: (context, appState, child) {
                  return CustomScrollView(
                    controller: _scrollController,
                    physics: const BouncingScrollPhysics(),
                    slivers: [
                      SliverPadding(
                        padding: const EdgeInsets.all(16),
                        sliver: SliverMasonryGrid.count(
                          crossAxisCount: 2,
                          mainAxisSpacing: 16,
                          crossAxisSpacing: 16,
                          childCount: _buildGridItems().length,
                          itemBuilder: (context, index) {
                            return _buildGridItems()[index];
                          },
                        ),
                      ),
                      
                      // Bottom padding
                      const SliverToBoxAdapter(
                        child: SizedBox(height: 32),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
