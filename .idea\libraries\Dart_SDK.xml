<component name="libraryTable">
  <library name="Dart <PERSON>K">
    <CLASSES>
      <root url="file://F:\flutter_windows_3.16.9-stable\flutter/bin/cache/dart-sdk/lib/async" />
      <root url="file://F:\flutter_windows_3.16.9-stable\flutter/bin/cache/dart-sdk/lib/collection" />
      <root url="file://F:\flutter_windows_3.16.9-stable\flutter/bin/cache/dart-sdk/lib/convert" />
      <root url="file://F:\flutter_windows_3.16.9-stable\flutter/bin/cache/dart-sdk/lib/core" />
      <root url="file://F:\flutter_windows_3.16.9-stable\flutter/bin/cache/dart-sdk/lib/developer" />
      <root url="file://F:\flutter_windows_3.16.9-stable\flutter/bin/cache/dart-sdk/lib/html" />
      <root url="file://F:\flutter_windows_3.16.9-stable\flutter/bin/cache/dart-sdk/lib/io" />
      <root url="file://F:\flutter_windows_3.16.9-stable\flutter/bin/cache/dart-sdk/lib/isolate" />
      <root url="file://F:\flutter_windows_3.16.9-stable\flutter/bin/cache/dart-sdk/lib/math" />
      <root url="file://F:\flutter_windows_3.16.9-stable\flutter/bin/cache/dart-sdk/lib/mirrors" />
      <root url="file://F:\flutter_windows_3.16.9-stable\flutter/bin/cache/dart-sdk/lib/typed_data" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>