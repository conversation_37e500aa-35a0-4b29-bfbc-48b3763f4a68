name: mediacraft
description: A comprehensive multi-tool mobile application for media processing
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI & Navigation
  cupertino_icons: ^1.0.6
  flutter_staggered_grid_view: ^0.7.0
  animations: ^2.0.8
  lottie: ^3.1.0
  
  # State Management
  provider: ^6.1.1
  
  # File Handling & Permissions
  file_picker: ^6.1.1
  path_provider: ^2.1.1
  permission_handler: ^11.1.0
  
  # Video Processing
  ffmpeg_kit_flutter: ^6.0.3
  video_player: ^2.8.1
  video_thumbnail: ^0.5.3
  
  # Audio Processing
  audioplayers: ^5.2.1
  just_audio: ^0.9.36
  audio_waveforms: ^1.0.5
  
  # Image Processing
  image: ^4.1.3
  flutter_image_compress: ^2.1.0
  
  # PDF Processing
  pdf: ^3.10.7
  printing: ^5.11.1
  syncfusion_flutter_pdf: ^23.2.7
  
  # Live Wallpaper (Android)
  android_intent_plus: ^4.0.3
  
  # Ads
  google_mobile_ads: ^4.0.0
  
  # Utilities
  share_plus: ^7.2.1
  url_launcher: ^6.2.1
  flutter_native_splash: ^2.3.6
  
  # Storage & Database
  shared_preferences: ^2.2.2
  sqflite: ^2.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/animations/
    - assets/icons/
  
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/icons/app_icon_foreground.png"

flutter_native_splash:
  color: "#ffffff"
  image: assets/images/splash_logo.png
  android_12:
    image: assets/images/splash_logo.png
    color: "#ffffff"
