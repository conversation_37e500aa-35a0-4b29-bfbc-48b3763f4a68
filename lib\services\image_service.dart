import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image/image.dart' as img;

class ImageService {
  /// Compress image with target size and quality
  static Future<String?> compressImage({
    required String inputPath,
    required int targetSizeKB,
    required int quality,
    bool optimizeForWeb = false,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/compressed_image_${DateTime.now().millisecondsSinceEpoch}.jpg';

      onProgress?.call(0.3);

      // First attempt with specified quality
      var result = await FlutterImageCompress.compressAndGetFile(
        inputPath,
        outputPath,
        quality: quality,
        format: CompressFormat.jpeg,
      );

      onProgress?.call(0.7);

      if (result == null) {
        return null;
      }

      // Check if we need to compress further to reach target size
      var currentSize = await result.length();
      var currentSizeKB = currentSize / 1024;

      if (currentSizeKB > targetSizeKB) {
        // Iteratively reduce quality to reach target size
        int adjustedQuality = quality;
        while (currentSizeKB > targetSizeKB && adjustedQuality > 10) {
          adjustedQuality -= 10;
          
          result = await FlutterImageCompress.compressAndGetFile(
            inputPath,
            outputPath,
            quality: adjustedQuality,
            format: CompressFormat.jpeg,
          );

          if (result == null) break;
          
          currentSize = await result.length();
          currentSizeKB = currentSize / 1024;
        }
      }

      onProgress?.call(1.0);
      return result?.path;
    } catch (e) {
      debugPrint('Error compressing image: $e');
      return null;
    }
  }

  /// Convert image format
  static Future<String?> convertImageFormat({
    required String inputPath,
    required String outputFormat,
    int quality = 90,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/converted_image_${DateTime.now().millisecondsSinceEpoch}.$outputFormat';

      onProgress?.call(0.3);

      // Read the input image
      final inputFile = File(inputPath);
      final inputBytes = await inputFile.readAsBytes();
      final image = img.decodeImage(inputBytes);

      if (image == null) {
        return null;
      }

      onProgress?.call(0.6);

      // Convert to desired format
      Uint8List? outputBytes;
      switch (outputFormat.toLowerCase()) {
        case 'jpg':
        case 'jpeg':
          outputBytes = Uint8List.fromList(img.encodeJpg(image, quality: quality));
          break;
        case 'png':
          outputBytes = Uint8List.fromList(img.encodePng(image));
          break;
        case 'webp':
          outputBytes = Uint8List.fromList(img.encodeWebP(image, quality: quality));
          break;
        case 'bmp':
          outputBytes = Uint8List.fromList(img.encodeBmp(image));
          break;
        default:
          return null;
      }

      if (outputBytes == null) {
        return null;
      }

      onProgress?.call(0.9);

      // Write the output file
      final outputFile = File(outputPath);
      await outputFile.writeAsBytes(outputBytes);

      onProgress?.call(1.0);
      return outputPath;
    } catch (e) {
      debugPrint('Error converting image format: $e');
      return null;
    }
  }

  /// Resize image
  static Future<String?> resizeImage({
    required String inputPath,
    required int width,
    required int height,
    bool maintainAspectRatio = true,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/resized_image_${DateTime.now().millisecondsSinceEpoch}.jpg';

      onProgress?.call(0.3);

      // Read the input image
      final inputFile = File(inputPath);
      final inputBytes = await inputFile.readAsBytes();
      final image = img.decodeImage(inputBytes);

      if (image == null) {
        return null;
      }

      onProgress?.call(0.6);

      // Resize the image
      img.Image resizedImage;
      if (maintainAspectRatio) {
        resizedImage = img.copyResize(
          image,
          width: width,
          height: height,
          interpolation: img.Interpolation.linear,
        );
      } else {
        resizedImage = img.copyResize(
          image,
          width: width,
          height: height,
          interpolation: img.Interpolation.linear,
        );
      }

      onProgress?.call(0.9);

      // Encode and save
      final outputBytes = img.encodeJpg(resizedImage, quality: 90);
      final outputFile = File(outputPath);
      await outputFile.writeAsBytes(outputBytes);

      onProgress?.call(1.0);
      return outputPath;
    } catch (e) {
      debugPrint('Error resizing image: $e');
      return null;
    }
  }

  /// Apply filters to image
  static Future<String?> applyImageFilter({
    required String inputPath,
    required String filterType,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath = '${directory.path}/filtered_image_${DateTime.now().millisecondsSinceEpoch}.jpg';

      onProgress?.call(0.3);

      // Read the input image
      final inputFile = File(inputPath);
      final inputBytes = await inputFile.readAsBytes();
      final image = img.decodeImage(inputBytes);

      if (image == null) {
        return null;
      }

      onProgress?.call(0.6);

      // Apply filter
      img.Image filteredImage = image;
      switch (filterType.toLowerCase()) {
        case 'grayscale':
          filteredImage = img.grayscale(image);
          break;
        case 'sepia':
          filteredImage = img.sepia(image);
          break;
        case 'invert':
          filteredImage = img.invert(image);
          break;
        case 'blur':
          filteredImage = img.gaussianBlur(image, radius: 5);
          break;
        case 'emboss':
          filteredImage = img.emboss(image);
          break;
        default:
          filteredImage = image;
      }

      onProgress?.call(0.9);

      // Encode and save
      final outputBytes = img.encodeJpg(filteredImage, quality: 90);
      final outputFile = File(outputPath);
      await outputFile.writeAsBytes(outputBytes);

      onProgress?.call(1.0);
      return outputPath;
    } catch (e) {
      debugPrint('Error applying image filter: $e');
      return null;
    }
  }

  /// Get image information
  static Future<Map<String, dynamic>?> getImageInfo(String imagePath) async {
    try {
      final file = File(imagePath);
      final bytes = await file.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        return null;
      }

      final fileSize = await file.length();

      return {
        'path': imagePath,
        'width': image.width,
        'height': image.height,
        'fileSize': fileSize,
        'format': imagePath.split('.').last.toUpperCase(),
      };
    } catch (e) {
      debugPrint('Error getting image info: $e');
      return null;
    }
  }

  /// Batch process images
  static Future<List<String>?> batchProcessImages({
    required List<String> inputPaths,
    required String operation,
    Map<String, dynamic>? parameters,
    Function(double)? onProgress,
  }) async {
    try {
      final outputPaths = <String>[];
      
      for (int i = 0; i < inputPaths.length; i++) {
        final inputPath = inputPaths[i];
        String? outputPath;

        switch (operation) {
          case 'compress':
            outputPath = await compressImage(
              inputPath: inputPath,
              targetSizeKB: parameters?['targetSizeKB'] ?? 100,
              quality: parameters?['quality'] ?? 85,
              optimizeForWeb: parameters?['optimizeForWeb'] ?? false,
            );
            break;
          case 'convert':
            outputPath = await convertImageFormat(
              inputPath: inputPath,
              outputFormat: parameters?['outputFormat'] ?? 'jpg',
              quality: parameters?['quality'] ?? 90,
            );
            break;
          case 'resize':
            outputPath = await resizeImage(
              inputPath: inputPath,
              width: parameters?['width'] ?? 800,
              height: parameters?['height'] ?? 600,
              maintainAspectRatio: parameters?['maintainAspectRatio'] ?? true,
            );
            break;
        }

        if (outputPath != null) {
          outputPaths.add(outputPath);
        }

        onProgress?.call((i + 1) / inputPaths.length);
      }

      return outputPaths.isNotEmpty ? outputPaths : null;
    } catch (e) {
      debugPrint('Error batch processing images: $e');
      return null;
    }
  }
}
