import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:syncfusion_flutter_pdf/pdf.dart' as sf;

class PdfService {
  /// Convert images to PDF
  static Future<String?> convertImagesToPdf({
    required List<String> imagePaths,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/images_to_pdf_${DateTime.now().millisecondsSinceEpoch}.pdf';

      onProgress?.call(0.2);

      // Create PDF document
      final pdf = pw.Document();

      for (int i = 0; i < imagePaths.length; i++) {
        final imagePath = imagePaths[i];
        final imageFile = File(imagePath);
        final imageBytes = await imageFile.readAsBytes();

        // Add image to PDF page
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            build: (pw.Context context) {
              return pw.Center(
                child: pw.Image(
                  pw.MemoryImage(imageBytes),
                  fit: pw.BoxFit.contain,
                ),
              );
            },
          ),
        );

        onProgress?.call(0.2 + (0.6 * (i + 1) / imagePaths.length));
      }

      onProgress?.call(0.8);

      // Save PDF
      final outputFile = File(outputPath);
      await outputFile.writeAsBytes(await pdf.save());

      onProgress?.call(1.0);
      return outputPath;
    } catch (e) {
      debugPrint('Error converting images to PDF: $e');
      return null;
    }
  }

  /// Convert PDF to images
  static Future<List<String>?> convertPdfToImages({
    required String pdfPath,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPaths = <String>[];

      onProgress?.call(0.2);

      // Load PDF document
      final pdfFile = File(pdfPath);
      final pdfBytes = await pdfFile.readAsBytes();
      final document = sf.PdfDocument(inputBytes: pdfBytes);

      onProgress?.call(0.3);

      // Convert each page to image
      // Note: This is a simplified implementation
      // For actual PDF to image conversion, you might need additional packages
      for (int i = 0; i < document.pages.count; i++) {
        final outputPath =
            '${directory.path}/pdf_page_${i + 1}_${DateTime.now().millisecondsSinceEpoch}.png';

        // This is a placeholder - actual implementation would require
        // additional PDF rendering capabilities
        outputPaths.add(outputPath);

        onProgress?.call(0.3 + (0.6 * (i + 1) / document.pages.count));
      }

      document.dispose();

      onProgress?.call(1.0);
      return outputPaths.isNotEmpty ? outputPaths : null;
    } catch (e) {
      debugPrint('Error converting PDF to images: $e');
      return null;
    }
  }

  /// Compress PDF
  static Future<String?> compressPdf({
    required String inputPath,
    required int compressionLevel, // 1 = low, 2 = medium, 3 = high
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/compressed_pdf_${DateTime.now().millisecondsSinceEpoch}.pdf';

      onProgress?.call(0.2);

      // Load PDF document
      final pdfFile = File(inputPath);
      final pdfBytes = await pdfFile.readAsBytes();
      final document = sf.PdfDocument(inputBytes: pdfBytes);

      onProgress?.call(0.4);

      // Apply compression settings based on level
      sf.PdfCompressionLevel pdfCompressionLevel;
      switch (compressionLevel) {
        case 1:
          pdfCompressionLevel = sf.PdfCompressionLevel.none;
          break;
        case 2:
          pdfCompressionLevel = sf.PdfCompressionLevel.normal;
          break;
        case 3:
          pdfCompressionLevel = sf.PdfCompressionLevel.best;
          break;
        default:
          pdfCompressionLevel = sf.PdfCompressionLevel.normal;
      }

      // Set compression options
      document.compressionLevel = pdfCompressionLevel;
      document.colorSpace = sf.PdfColorSpace.rgb;

      onProgress?.call(0.7);

      // Save compressed PDF
      final compressedBytes = await document.save();
      document.dispose();

      final outputFile = File(outputPath);
      await outputFile.writeAsBytes(compressedBytes);

      onProgress?.call(1.0);
      return outputPath;
    } catch (e) {
      debugPrint('Error compressing PDF: $e');
      return null;
    }
  }

  /// Merge multiple PDFs
  static Future<String?> mergePdfs({
    required List<String> pdfPaths,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/merged_pdf_${DateTime.now().millisecondsSinceEpoch}.pdf';

      onProgress?.call(0.2);

      // Create new PDF document
      final mergedDocument = sf.PdfDocument();

      for (int i = 0; i < pdfPaths.length; i++) {
        final pdfPath = pdfPaths[i];
        final pdfFile = File(pdfPath);
        final pdfBytes = await pdfFile.readAsBytes();
        final document = sf.PdfDocument(inputBytes: pdfBytes);

        // Import pages from current document
        for (int j = 0; j < document.pages.count; j++) {
          mergedDocument.pages.add().graphics.drawPdfTemplate(
                document.pages[j].createTemplate(),
                const Offset(0, 0),
              );
        }
        document.dispose();

        onProgress?.call(0.2 + (0.6 * (i + 1) / pdfPaths.length));
      }

      onProgress?.call(0.8);

      // Save merged PDF
      final mergedBytes = await mergedDocument.save();
      mergedDocument.dispose();

      final outputFile = File(outputPath);
      await outputFile.writeAsBytes(mergedBytes);

      onProgress?.call(1.0);
      return outputPath;
    } catch (e) {
      debugPrint('Error merging PDFs: $e');
      return null;
    }
  }

  /// Split PDF into separate pages
  static Future<List<String>?> splitPdf({
    required String pdfPath,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPaths = <String>[];

      onProgress?.call(0.2);

      // Load PDF document
      final pdfFile = File(pdfPath);
      final pdfBytes = await pdfFile.readAsBytes();
      final document = sf.PdfDocument(inputBytes: pdfBytes);

      onProgress?.call(0.3);

      // Split each page into separate PDF
      for (int i = 0; i < document.pages.count; i++) {
        final singlePageDocument = sf.PdfDocument();
        singlePageDocument.pages.add().graphics.drawPdfTemplate(
              document.pages[i].createTemplate(),
              const Offset(0, 0),
            );

        final outputPath =
            '${directory.path}/split_page_${i + 1}_${DateTime.now().millisecondsSinceEpoch}.pdf';
        final pageBytes = await singlePageDocument.save();
        singlePageDocument.dispose();

        final outputFile = File(outputPath);
        await outputFile.writeAsBytes(pageBytes);

        outputPaths.add(outputPath);

        onProgress?.call(0.3 + (0.6 * (i + 1) / document.pages.count));
      }

      document.dispose();

      onProgress?.call(1.0);
      return outputPaths.isNotEmpty ? outputPaths : null;
    } catch (e) {
      debugPrint('Error splitting PDF: $e');
      return null;
    }
  }

  /// Get PDF information
  static Future<Map<String, dynamic>?> getPdfInfo(String pdfPath) async {
    try {
      final pdfFile = File(pdfPath);
      final pdfBytes = await pdfFile.readAsBytes();
      final document = sf.PdfDocument(inputBytes: pdfBytes);

      final info = {
        'path': pdfPath,
        'pageCount': document.pages.count,
        'fileSize': await pdfFile.length(),
        'title': document.documentInformation.title,
        'author': document.documentInformation.author,
        'subject': document.documentInformation.subject,
        'creator': document.documentInformation.creator,
        'producer': document.documentInformation.producer,
        'creationDate': document.documentInformation.creationDate,
        'modificationDate': document.documentInformation.modificationDate,
      };

      document.dispose();
      return info;
    } catch (e) {
      debugPrint('Error getting PDF info: $e');
      return null;
    }
  }

  /// Add watermark to PDF
  static Future<String?> addWatermarkToPdf({
    required String pdfPath,
    required String watermarkText,
    double opacity = 0.5,
    Function(double)? onProgress,
  }) async {
    try {
      onProgress?.call(0.1);

      final directory = await getTemporaryDirectory();
      final outputPath =
          '${directory.path}/watermarked_pdf_${DateTime.now().millisecondsSinceEpoch}.pdf';

      onProgress?.call(0.2);

      // Load PDF document
      final pdfFile = File(pdfPath);
      final pdfBytes = await pdfFile.readAsBytes();
      final document = sf.PdfDocument(inputBytes: pdfBytes);

      onProgress?.call(0.4);

      // Add watermark to each page
      for (int i = 0; i < document.pages.count; i++) {
        final page = document.pages[i];
        final graphics = page.graphics;

        // Create watermark text
        final font = sf.PdfStandardFont(sf.PdfFontFamily.helvetica, 50);
        final brush = sf.PdfSolidBrush(
            sf.PdfColor(128, 128, 128, (opacity * 255).toInt()));

        // Calculate position (center of page)
        final pageSize = page.size;
        final textSize = font.measureString(watermarkText);
        final x = (pageSize.width - textSize.width) / 2;
        final y = (pageSize.height - textSize.height) / 2;

        // Draw watermark
        graphics.save();
        graphics.translateTransform(
            x + textSize.width / 2, y + textSize.height / 2);
        graphics.rotateTransform(-45); // Diagonal watermark
        graphics.drawString(
          watermarkText,
          font,
          brush: brush,
          bounds: Rect.fromLTWH(-textSize.width / 2, -textSize.height / 2,
              textSize.width, textSize.height),
        );
        graphics.restore();

        onProgress?.call(0.4 + (0.4 * (i + 1) / document.pages.count));
      }

      onProgress?.call(0.8);

      // Save watermarked PDF
      final watermarkedBytes = await document.save();
      document.dispose();

      final outputFile = File(outputPath);
      await outputFile.writeAsBytes(watermarkedBytes);

      onProgress?.call(1.0);
      return outputPath;
    } catch (e) {
      debugPrint('Error adding watermark to PDF: $e');
      return null;
    }
  }
}
