import 'package:flutter/material.dart';

enum ToolCategory {
  video,
  audio,
  image,
  pdf,
}

class ToolModel {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final ToolCategory category;
  final String route;
  final Color color;
  final List<String> features;

  const ToolModel({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.category,
    required this.route,
    required this.color,
    required this.features,
  });

  static List<ToolModel> getAllTools() {
    return [
      // Video Tools
      const ToolModel(
        id: 'video_wallpaper',
        title: 'Video to Live Wallpaper',
        description: 'Convert videos to live wallpapers for home and lock screen',
        icon: Icons.wallpaper,
        category: ToolCategory.video,
        route: '/video-to-wallpaper',
        color: Color(0xFF6366F1),
        features: ['Live wallpaper', 'Home screen', 'Lock screen', 'Video preview'],
      ),
      const ToolModel(
        id: 'video_watermark',
        title: 'Video Watermark',
        description: 'Add text or image watermarks to videos with lossless quality',
        icon: Icons.branding_watermark,
        category: ToolCategory.video,
        route: '/video-watermark',
        color: Color(0xFF8B5CF6),
        features: ['Text watermark', 'Image watermark', 'Transparent overlay', 'Lossless export'],
      ),
      
      // Audio Tools
      const ToolModel(
        id: 'mp3_cutter',
        title: 'MP3 Cutter & Ringtone Maker',
        description: 'Cut audio files and create custom ringtones',
        icon: Icons.content_cut,
        category: ToolCategory.audio,
        route: '/mp3-cutter',
        color: Color(0xFF06B6D4),
        features: ['Audio trimming', 'Ringtone maker', 'Multiple formats', 'Waveform editor'],
      ),
      const ToolModel(
        id: 'audio_editor',
        title: 'Simple Audio Editor',
        description: 'Apply effects like reverb, slow motion, and reverse',
        icon: Icons.audiotrack,
        category: ToolCategory.audio,
        route: '/audio-editor',
        color: Color(0xFF10B981),
        features: ['Reverb effect', 'Slow motion', 'Reverse audio', 'Real-time preview'],
      ),
      
      // Image Tools
      const ToolModel(
        id: 'image_compressor',
        title: 'Image Compressor',
        description: 'Reduce image file size with custom target sizes',
        icon: Icons.compress,
        category: ToolCategory.image,
        route: '/image-compressor',
        color: Color(0xFFF59E0B),
        features: ['Size reduction', 'Target size', 'Web optimization', 'Batch processing'],
      ),
      const ToolModel(
        id: 'image_converter',
        title: 'Image Converter',
        description: 'Convert between different image formats',
        icon: Icons.transform,
        category: ToolCategory.image,
        route: '/image-converter',
        color: Color(0xFFEF4444),
        features: ['Format conversion', 'PNG to JPG', 'WEBP support', 'Quality control'],
      ),
      
      // PDF Tools
      const ToolModel(
        id: 'pdf_converter',
        title: 'PDF Converter',
        description: 'Convert images to PDF and PDF to images',
        icon: Icons.picture_as_pdf,
        category: ToolCategory.pdf,
        route: '/pdf-converter',
        color: Color(0xFF8B5CF6),
        features: ['Images to PDF', 'PDF to images', 'Multiple pages', 'Custom layout'],
      ),
      const ToolModel(
        id: 'pdf_compressor',
        title: 'PDF Compressor',
        description: 'Reduce PDF file size while maintaining quality',
        icon: Icons.compress,
        category: ToolCategory.pdf,
        route: '/pdf-compressor',
        color: Color(0xFF6366F1),
        features: ['Size reduction', 'Quality control', 'Fast processing', 'Batch support'],
      ),
    ];
  }

  static List<ToolModel> getToolsByCategory(ToolCategory category) {
    return getAllTools().where((tool) => tool.category == category).toList();
  }

  static ToolModel? getToolById(String id) {
    try {
      return getAllTools().firstWhere((tool) => tool.id == id);
    } catch (e) {
      return null;
    }
  }
}
